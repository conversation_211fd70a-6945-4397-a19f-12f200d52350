2025-06-03 10:18:49 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:95 - Step 1 completed successfully.
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:101 - --- Step 2: HSI Pose Synchronization ---
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:105 - Step 2 completed successfully.
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:111 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:115 - Step 3 completed successfully.
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:121 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:127 - Step 4 completed successfully.
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:133 - --- Step 5: Plot Generation ---
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:139 - Step 5 completed successfully.
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:145 - Complete HSI Georeferencing Pipeline executed successfully.
2025-06-03 10:18:49 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:95 - Step 1 completed successfully.
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:101 - --- Step 2: HSI Pose Synchronization ---
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - ERROR - main_pipeline:107 - ERROR in Step 2: HSI Pose Synchronization. HSI pose synchronization failed
2025-06-03 10:18:49 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:95 - Step 1 completed successfully.
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:101 - --- Step 2: HSI Pose Synchronization ---
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:105 - Step 2 completed successfully.
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:111 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:115 - Step 3 completed successfully.
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:121 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - WARNING - main_pipeline:124 - Step 4: Error creating georeferenced RGB GeoTIFF.
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:133 - --- Step 5: Plot Generation ---
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - WARNING - main_pipeline:136 - Step 5: Error generating plots.
2025-06-03 10:18:49 - src.hsi_pipeline.main_pipeline - WARNING - main_pipeline:147 - HSI Georeferencing Pipeline completed with warnings or errors in optional steps.
2025-06-03 10:18:53 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-03 10:18:53 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:373 - HSI data directory: test_dir
2025-06-03 10:18:53 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:374 - HSI sync file: test_dir\test_hsi_sync.txt
2025-06-03 10:18:53 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:375 - HSI header file: test_dir\test_hsi.hdr
2025-06-03 10:18:53 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:376 - WebODM CSV file: output\webodm.csv
2025-06-03 10:18:53 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:377 - Output CSV file: output\hsi_poses.csv
2025-06-03 10:18:53 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:385 - Output directory created/verified: output
2025-06-03 10:18:53 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:390 - Loading HSI header file: test_dir\test_hsi.hdr
2025-06-03 10:18:53 - src.hsi_pipeline.synchronize_hsi_webodm - ERROR - synchronize_hsi_webodm:407 - Error loading HSI metadata: Test
2025-06-03 10:18:53 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-03 10:18:53 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:202 - Loaded 2 WebODM pose records from CSV
2025-06-03 10:18:53 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:229 - Successfully processed 2 WebODM poses
2025-06-03 10:18:53 - src.hsi_pipeline.synchronize_hsi_webodm - WARNING - synchronize_hsi_webodm:69 - 'lines' not found in C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-120\test_parse_hdr_file_missing_li0\test.hdr
2025-06-03 10:18:53 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-03 10:18:53 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-03 10:18:53 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-03 10:18:53 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-03 10:18:53 - src.hsi_pipeline.vectorized_georef - ERROR - vectorized_georef:364 - Invalid quaternion for line 0: [nan  0.  0.  1.], Error: Found zero norm quaternions in `quat`.
2025-06-03 10:44:51 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:95 - Step 1 completed successfully.
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:101 - --- Step 2: HSI Pose Synchronization ---
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:105 - Step 2 completed successfully.
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:111 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:115 - Step 3 completed successfully.
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:121 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:127 - Step 4 completed successfully.
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:133 - --- Step 5: Plot Generation ---
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:139 - Step 5 completed successfully.
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:145 - Complete HSI Georeferencing Pipeline executed successfully.
2025-06-03 10:44:51 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:95 - Step 1 completed successfully.
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:101 - --- Step 2: HSI Pose Synchronization ---
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - ERROR - main_pipeline:107 - ERROR in Step 2: HSI Pose Synchronization. HSI pose synchronization failed
2025-06-03 10:44:51 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:95 - Step 1 completed successfully.
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:101 - --- Step 2: HSI Pose Synchronization ---
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:105 - Step 2 completed successfully.
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:111 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:115 - Step 3 completed successfully.
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:121 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - WARNING - main_pipeline:124 - Step 4: Error creating georeferenced RGB GeoTIFF.
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:133 - --- Step 5: Plot Generation ---
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - WARNING - main_pipeline:136 - Step 5: Error generating plots.
2025-06-03 10:44:51 - src.hsi_pipeline.main_pipeline - WARNING - main_pipeline:147 - HSI Georeferencing Pipeline completed with warnings or errors in optional steps.
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:373 - HSI data directory: test_dir
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:374 - HSI sync file: test_dir\test_hsi_sync.txt
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:375 - HSI header file: test_dir\test_hsi.hdr
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:376 - WebODM CSV file: output\webodm.csv
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:377 - Output CSV file: output\hsi_poses.csv
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:385 - Output directory created/verified: output
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:390 - Loading HSI header file: test_dir\test_hsi.hdr
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - ERROR - synchronize_hsi_webodm:407 - Error loading HSI metadata: Test
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:202 - Loaded 2 WebODM pose records from CSV
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:229 - Successfully processed 2 WebODM poses
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - WARNING - synchronize_hsi_webodm:69 - 'lines' not found in C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-123\test_parse_hdr_file_missing_li0\test.hdr
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:373 - HSI data directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-123\test_run_synchronization_basic0\hsi
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:374 - HSI sync file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-123\test_run_synchronization_basic0\hsi\test_hsi_sync.txt
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:375 - HSI header file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-123\test_run_synchronization_basic0\hsi\test_hsi.hdr
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:376 - WebODM CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-123\test_run_synchronization_basic0\output\webodm_poses.csv
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:377 - Output CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-123\test_run_synchronization_basic0\output\synchronized_poses.csv
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:385 - Output directory created/verified: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-123\test_run_synchronization_basic0\output
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:390 - Loading HSI header file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-123\test_run_synchronization_basic0\hsi\test_hsi.hdr
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - ERROR - synchronize_hsi_webodm:407 - Error loading HSI metadata: Header file not found: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-123\test_run_synchronization_basic0\hsi\test_hsi.hdr
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:373 - HSI data directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-123\test_run_synchronization_with_0\hsi
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:374 - HSI sync file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-123\test_run_synchronization_with_0\hsi\test_hsi_sync.txt
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:375 - HSI header file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-123\test_run_synchronization_with_0\hsi\test_hsi.hdr
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:376 - WebODM CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-123\test_run_synchronization_with_0\output\webodm_poses.csv
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:377 - Output CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-123\test_run_synchronization_with_0\output\synchronized_poses.csv
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:385 - Output directory created/verified: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-123\test_run_synchronization_with_0\output
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:390 - Loading HSI header file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-123\test_run_synchronization_with_0\hsi\test_hsi.hdr
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - ERROR - synchronize_hsi_webodm:407 - Error loading HSI metadata: Header file not found: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-123\test_run_synchronization_with_0\hsi\test_hsi.hdr
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:373 - HSI data directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-123\test_run_synchronization_file_0\nonexistent
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:374 - HSI sync file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-123\test_run_synchronization_file_0\nonexistent\test_hsi_sync.txt
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:375 - HSI header file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-123\test_run_synchronization_file_0\nonexistent\test_hsi.hdr
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:376 - WebODM CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-123\test_run_synchronization_file_0\output\webodm_poses.csv
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:377 - Output CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-123\test_run_synchronization_file_0\output\synchronized_poses.csv
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:385 - Output directory created/verified: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-123\test_run_synchronization_file_0\output
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:390 - Loading HSI header file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-123\test_run_synchronization_file_0\nonexistent\test_hsi.hdr
2025-06-03 10:44:56 - src.hsi_pipeline.synchronize_hsi_webodm - ERROR - synchronize_hsi_webodm:407 - Error loading HSI metadata: Header file not found: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-123\test_run_synchronization_file_0\nonexistent\test_hsi.hdr
2025-06-03 10:44:57 - src.hsi_pipeline.vectorized_georef - ERROR - vectorized_georef:364 - Invalid quaternion for line 0: [nan  0.  0.  1.], Error: Found zero norm quaternions in `quat`.
2025-06-03 10:51:48 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:95 - Step 1 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:101 - --- Step 2: HSI Pose Synchronization ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:105 - Step 2 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:111 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:115 - Step 3 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:121 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:127 - Step 4 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:133 - --- Step 5: Plot Generation ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:139 - Step 5 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:145 - Complete HSI Georeferencing Pipeline executed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:95 - Step 1 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:101 - --- Step 2: HSI Pose Synchronization ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - ERROR - main_pipeline:107 - ERROR in Step 2: HSI Pose Synchronization. HSI pose synchronization failed
2025-06-03 10:51:48 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:95 - Step 1 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:101 - --- Step 2: HSI Pose Synchronization ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:105 - Step 2 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:111 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:115 - Step 3 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:121 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - WARNING - main_pipeline:124 - Step 4: Error creating georeferenced RGB GeoTIFF.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:133 - --- Step 5: Plot Generation ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - WARNING - main_pipeline:136 - Step 5: Error generating plots.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - WARNING - main_pipeline:147 - HSI Georeferencing Pipeline completed with warnings or errors in optional steps.
2025-06-03 10:51:48 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: invalid_config.toml
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - ERROR - main_pipeline:150 - Configuration error: Config error
2025-06-03 10:51:48 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - ERROR - main_pipeline:97 - ERROR in Step 1: WebODM Pose Consolidation. Unexpected error
2025-06-03 10:51:48 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:95 - Step 1 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:101 - --- Step 2: HSI Pose Synchronization ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:105 - Step 2 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:111 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:115 - Step 3 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:121 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:127 - Step 4 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:133 - --- Step 5: Plot Generation ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:139 - Step 5 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:145 - Complete HSI Georeferencing Pipeline executed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - ERROR - main_pipeline:150 - Configuration error: Test
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:95 - Step 1 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:101 - --- Step 2: HSI Pose Synchronization ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:105 - Step 2 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:111 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:115 - Step 3 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:121 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:127 - Step 4 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:133 - --- Step 5: Plot Generation ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:139 - Step 5 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:145 - Complete HSI Georeferencing Pipeline executed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - ERROR - main_pipeline:97 - ERROR in Step 1: WebODM Pose Consolidation. Consolidation failed
2025-06-03 10:51:48 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:95 - Step 1 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:101 - --- Step 2: HSI Pose Synchronization ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:105 - Step 2 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:111 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - ERROR - main_pipeline:117 - ERROR in Step 3: Direct HSI Pixel Georeferencing. Georeferencing failed
2025-06-03 10:51:48 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:95 - Step 1 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:101 - --- Step 2: HSI Pose Synchronization ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:105 - Step 2 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:111 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:115 - Step 3 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:121 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - WARNING - main_pipeline:129 - Step 4: Error creating georeferenced RGB GeoTIFF: RGB creation failed
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:133 - --- Step 5: Plot Generation ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:139 - Step 5 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - WARNING - main_pipeline:147 - HSI Georeferencing Pipeline completed with warnings or errors in optional steps.
2025-06-03 10:51:48 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:95 - Step 1 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:101 - --- Step 2: HSI Pose Synchronization ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:105 - Step 2 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:111 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:115 - Step 3 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:121 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:127 - Step 4 completed successfully.
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:133 - --- Step 5: Plot Generation ---
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - WARNING - main_pipeline:141 - Step 5: Error generating plots: Plotting failed
2025-06-03 10:51:48 - src.hsi_pipeline.main_pipeline - WARNING - main_pipeline:147 - HSI Georeferencing Pipeline completed with warnings or errors in optional steps.
2025-06-03 10:51:49 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:51:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:51:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:51:49 - src.hsi_pipeline.main_pipeline - ERROR - main_pipeline:97 - ERROR in Step 1: WebODM Pose Consolidation. Unexpected runtime error
2025-06-03 10:51:49 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:51:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:51:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:51:49 - src.hsi_pipeline.main_pipeline - ERROR - main_pipeline:97 - ERROR in Step 1: WebODM Pose Consolidation. WebODM pose consolidation failed
2025-06-03 10:51:49 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:51:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:51:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:51:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:95 - Step 1 completed successfully.
2025-06-03 10:51:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:101 - --- Step 2: HSI Pose Synchronization ---
2025-06-03 10:51:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:105 - Step 2 completed successfully.
2025-06-03 10:51:49 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:111 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-03 10:51:49 - src.hsi_pipeline.main_pipeline - ERROR - main_pipeline:117 - ERROR in Step 3: Direct HSI Pixel Georeferencing. HSI pixel georeferencing failed
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:373 - HSI data directory: test_dir
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:374 - HSI sync file: test_dir\test_hsi_sync.txt
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:375 - HSI header file: test_dir\test_hsi.hdr
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:376 - WebODM CSV file: output\webodm.csv
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:377 - Output CSV file: output\hsi_poses.csv
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:385 - Output directory created/verified: output
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:390 - Loading HSI header file: test_dir\test_hsi.hdr
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - ERROR - synchronize_hsi_webodm:407 - Error loading HSI metadata: Test
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:202 - Loaded 2 WebODM pose records from CSV
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:229 - Successfully processed 2 WebODM poses
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - WARNING - synchronize_hsi_webodm:69 - 'lines' not found in C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-124\test_parse_hdr_file_missing_li0\test.hdr
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:373 - HSI data directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-124\test_run_synchronization_basic0\hsi
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:374 - HSI sync file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-124\test_run_synchronization_basic0\hsi\test_hsi_sync.txt
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:375 - HSI header file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-124\test_run_synchronization_basic0\hsi\test_hsi.hdr
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:376 - WebODM CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-124\test_run_synchronization_basic0\output\webodm_poses.csv
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:377 - Output CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-124\test_run_synchronization_basic0\output\synchronized_poses.csv
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:385 - Output directory created/verified: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-124\test_run_synchronization_basic0\output
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:390 - Loading HSI header file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-124\test_run_synchronization_basic0\hsi\test_hsi.hdr
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - ERROR - synchronize_hsi_webodm:407 - Error loading HSI metadata: Header file not found: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-124\test_run_synchronization_basic0\hsi\test_hsi.hdr
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:373 - HSI data directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-124\test_run_synchronization_with_0\hsi
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:374 - HSI sync file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-124\test_run_synchronization_with_0\hsi\test_hsi_sync.txt
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:375 - HSI header file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-124\test_run_synchronization_with_0\hsi\test_hsi.hdr
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:376 - WebODM CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-124\test_run_synchronization_with_0\output\webodm_poses.csv
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:377 - Output CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-124\test_run_synchronization_with_0\output\synchronized_poses.csv
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:385 - Output directory created/verified: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-124\test_run_synchronization_with_0\output
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:390 - Loading HSI header file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-124\test_run_synchronization_with_0\hsi\test_hsi.hdr
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - ERROR - synchronize_hsi_webodm:407 - Error loading HSI metadata: Header file not found: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-124\test_run_synchronization_with_0\hsi\test_hsi.hdr
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:373 - HSI data directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-124\test_run_synchronization_file_0\nonexistent
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:374 - HSI sync file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-124\test_run_synchronization_file_0\nonexistent\test_hsi_sync.txt
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:375 - HSI header file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-124\test_run_synchronization_file_0\nonexistent\test_hsi.hdr
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:376 - WebODM CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-124\test_run_synchronization_file_0\output\webodm_poses.csv
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:377 - Output CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-124\test_run_synchronization_file_0\output\synchronized_poses.csv
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:385 - Output directory created/verified: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-124\test_run_synchronization_file_0\output
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:390 - Loading HSI header file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-124\test_run_synchronization_file_0\nonexistent\test_hsi.hdr
2025-06-03 10:51:49 - src.hsi_pipeline.synchronize_hsi_webodm - ERROR - synchronize_hsi_webodm:407 - Error loading HSI metadata: Header file not found: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-124\test_run_synchronization_file_0\nonexistent\test_hsi.hdr
2025-06-03 10:51:49 - src.hsi_pipeline.vectorized_georef - ERROR - vectorized_georef:364 - Invalid quaternion for line 0: [nan  0.  0.  1.], Error: Found zero norm quaternions in `quat`.
2025-06-03 10:52:55 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:95 - Step 1 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:101 - --- Step 2: HSI Pose Synchronization ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:105 - Step 2 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:111 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:115 - Step 3 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:121 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:127 - Step 4 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:133 - --- Step 5: Plot Generation ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:139 - Step 5 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:145 - Complete HSI Georeferencing Pipeline executed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:95 - Step 1 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:101 - --- Step 2: HSI Pose Synchronization ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - ERROR - main_pipeline:107 - ERROR in Step 2: HSI Pose Synchronization. HSI pose synchronization failed
2025-06-03 10:52:55 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:95 - Step 1 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:101 - --- Step 2: HSI Pose Synchronization ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:105 - Step 2 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:111 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:115 - Step 3 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:121 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - WARNING - main_pipeline:124 - Step 4: Error creating georeferenced RGB GeoTIFF.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:133 - --- Step 5: Plot Generation ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - WARNING - main_pipeline:136 - Step 5: Error generating plots.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - WARNING - main_pipeline:147 - HSI Georeferencing Pipeline completed with warnings or errors in optional steps.
2025-06-03 10:52:55 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: invalid_config.toml
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - ERROR - main_pipeline:150 - Configuration error: Config error
2025-06-03 10:52:55 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - ERROR - main_pipeline:97 - ERROR in Step 1: WebODM Pose Consolidation. Unexpected error
2025-06-03 10:52:55 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:95 - Step 1 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:101 - --- Step 2: HSI Pose Synchronization ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:105 - Step 2 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:111 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:115 - Step 3 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:121 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:127 - Step 4 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:133 - --- Step 5: Plot Generation ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:139 - Step 5 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:145 - Complete HSI Georeferencing Pipeline executed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - ERROR - main_pipeline:150 - Configuration error: Test
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:95 - Step 1 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:101 - --- Step 2: HSI Pose Synchronization ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:105 - Step 2 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:111 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:115 - Step 3 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:121 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:127 - Step 4 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:133 - --- Step 5: Plot Generation ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:139 - Step 5 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:145 - Complete HSI Georeferencing Pipeline executed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - ERROR - main_pipeline:97 - ERROR in Step 1: WebODM Pose Consolidation. Consolidation failed
2025-06-03 10:52:55 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:95 - Step 1 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:101 - --- Step 2: HSI Pose Synchronization ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:105 - Step 2 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:111 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - ERROR - main_pipeline:117 - ERROR in Step 3: Direct HSI Pixel Georeferencing. Georeferencing failed
2025-06-03 10:52:55 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:95 - Step 1 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:101 - --- Step 2: HSI Pose Synchronization ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:105 - Step 2 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:111 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:115 - Step 3 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:121 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - WARNING - main_pipeline:129 - Step 4: Error creating georeferenced RGB GeoTIFF: RGB creation failed
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:133 - --- Step 5: Plot Generation ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:139 - Step 5 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - WARNING - main_pipeline:147 - HSI Georeferencing Pipeline completed with warnings or errors in optional steps.
2025-06-03 10:52:55 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:95 - Step 1 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:101 - --- Step 2: HSI Pose Synchronization ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:105 - Step 2 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:111 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:115 - Step 3 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:121 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:127 - Step 4 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:133 - --- Step 5: Plot Generation ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - WARNING - main_pipeline:141 - Step 5: Error generating plots: Plotting failed
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - WARNING - main_pipeline:147 - HSI Georeferencing Pipeline completed with warnings or errors in optional steps.
2025-06-03 10:52:55 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - ERROR - main_pipeline:97 - ERROR in Step 1: WebODM Pose Consolidation. Unexpected runtime error
2025-06-03 10:52:55 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - ERROR - main_pipeline:97 - ERROR in Step 1: WebODM Pose Consolidation. WebODM pose consolidation failed
2025-06-03 10:52:55 - src.hsi_pipeline.logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:83 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:91 - --- Step 1: WebODM Pose Consolidation ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:95 - Step 1 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:101 - --- Step 2: HSI Pose Synchronization ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:105 - Step 2 completed successfully.
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - INFO - main_pipeline:111 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-03 10:52:55 - src.hsi_pipeline.main_pipeline - ERROR - main_pipeline:117 - ERROR in Step 3: Direct HSI Pixel Georeferencing. HSI pixel georeferencing failed
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:373 - HSI data directory: test_dir
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:374 - HSI sync file: test_dir\test_hsi_sync.txt
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:375 - HSI header file: test_dir\test_hsi.hdr
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:376 - WebODM CSV file: output\webodm.csv
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:377 - Output CSV file: output\hsi_poses.csv
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:385 - Output directory created/verified: output
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:390 - Loading HSI header file: test_dir\test_hsi.hdr
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - ERROR - synchronize_hsi_webodm:407 - Error loading HSI metadata: Test
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:202 - Loaded 2 WebODM pose records from CSV
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:229 - Successfully processed 2 WebODM poses
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - WARNING - synchronize_hsi_webodm:69 - 'lines' not found in C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-127\test_parse_hdr_file_missing_li0\test.hdr
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:373 - HSI data directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-127\test_run_synchronization_basic0\hsi
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:374 - HSI sync file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-127\test_run_synchronization_basic0\hsi\test_hsi_sync.txt
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:375 - HSI header file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-127\test_run_synchronization_basic0\hsi\test_hsi.hdr
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:376 - WebODM CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-127\test_run_synchronization_basic0\output\webodm_poses.csv
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:377 - Output CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-127\test_run_synchronization_basic0\output\synchronized_poses.csv
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:385 - Output directory created/verified: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-127\test_run_synchronization_basic0\output
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:390 - Loading HSI header file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-127\test_run_synchronization_basic0\hsi\test_hsi.hdr
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - ERROR - synchronize_hsi_webodm:407 - Error loading HSI metadata: Header file not found: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-127\test_run_synchronization_basic0\hsi\test_hsi.hdr
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:373 - HSI data directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-127\test_run_synchronization_with_0\hsi
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:374 - HSI sync file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-127\test_run_synchronization_with_0\hsi\test_hsi_sync.txt
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:375 - HSI header file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-127\test_run_synchronization_with_0\hsi\test_hsi.hdr
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:376 - WebODM CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-127\test_run_synchronization_with_0\output\webodm_poses.csv
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:377 - Output CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-127\test_run_synchronization_with_0\output\synchronized_poses.csv
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:385 - Output directory created/verified: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-127\test_run_synchronization_with_0\output
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:390 - Loading HSI header file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-127\test_run_synchronization_with_0\hsi\test_hsi.hdr
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - ERROR - synchronize_hsi_webodm:407 - Error loading HSI metadata: Header file not found: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-127\test_run_synchronization_with_0\hsi\test_hsi.hdr
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:373 - HSI data directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-127\test_run_synchronization_file_0\nonexistent
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:374 - HSI sync file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-127\test_run_synchronization_file_0\nonexistent\test_hsi_sync.txt
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:375 - HSI header file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-127\test_run_synchronization_file_0\nonexistent\test_hsi.hdr
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:376 - WebODM CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-127\test_run_synchronization_file_0\output\webodm_poses.csv
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:377 - Output CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-127\test_run_synchronization_file_0\output\synchronized_poses.csv
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:385 - Output directory created/verified: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-127\test_run_synchronization_file_0\output
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:390 - Loading HSI header file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-127\test_run_synchronization_file_0\nonexistent\test_hsi.hdr
2025-06-03 10:52:55 - src.hsi_pipeline.synchronize_hsi_webodm - ERROR - synchronize_hsi_webodm:407 - Error loading HSI metadata: Header file not found: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-127\test_run_synchronization_file_0\nonexistent\test_hsi.hdr
2025-06-03 10:52:55 - src.hsi_pipeline.vectorized_georef - ERROR - vectorized_georef:364 - Invalid quaternion for line 0: [nan  0.  0.  1.], Error: Found zero norm quaternions in `quat`.
