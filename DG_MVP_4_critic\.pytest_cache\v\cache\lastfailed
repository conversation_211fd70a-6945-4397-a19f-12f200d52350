{"test_synchronize_hsi_webodm.py::TestParseHdrFile": true, "test_synchronize_hsi_webodm.py::TestConvertHsiTimestamp": true, "test_synchronize_hsi_webodm.py::TestInterpolatePose": true, "test_synchronize_hsi_webodm.py::TestRunSynchronization": true, "test_synchronize_hsi_webodm.py::TestRunSynchronization::test_run_synchronization_basic_functionality": true, "test_synchronize_hsi_webodm.py::TestRunSynchronization::test_run_synchronization_with_time_threshold": true, "test_synchronize_hsi_webodm.py::TestRunSynchronization::test_run_synchronization_missing_config_keys": true, "test_synchronize_hsi_webodm.py::TestRunSynchronization::test_run_synchronization_file_not_found": true, "tests/test_georeferencing.py::TestGeoreferencingLS2Updates::test_function_signature_accepts_config_dict": true, "tests/test_georeferencing.py::TestVectorizationIntegration::test_vectorized_processing_called_for_flat_plane": true, "tests/test_georeferencing.py::TestVectorizedExceptionHandling::test_vectorized_processing_specific_exception_fallback": true, "tests/test_georeferencing.py::TestVectorizedExceptionHandling::test_run_georeferencing_fallback_on_pose_transformation_error": true, "tests/test_georeferencing.py::TestLS5IncreasedCoverage::test_parse_sensor_model_fallback_to_two_column_format": true, "tests/test_georeferencing.py::TestLS5IncreasedCoverage::test_run_georeferencing_dsm_file_not_found": true, "tests/test_georeferencing.py::TestLS5IncreasedCoverage::test_run_georeferencing_missing_poses_file": true, "tests/test_georeferencing.py::TestLS5IncreasedCoverage::test_run_georeferencing_pose_line_mismatch": true, "tests/test_georeferencing.py::TestLS6VectorizedPathFix::test_run_georeferencing_flat_plane_vectorized_invocation": true, "tests/test_georeferencing.py::TestLS6VectorizedPathFix::test_run_georeferencing_flat_plane_vectorized_result_processing": true, "tests/test_georeferencing.py::TestLS6DSMPathResolution::test_dsm_path_resolution_relative_to_config": true, "tests/test_georeferencing.py::TestLS6DSMPathResolution::test_dsm_path_resolution_absolute_path_unchanged": true, "tests/test_georeferencing.py::TestLS6DSMPathResolution::test_dsm_path_resolution_relative_file_not_found_at_resolved_path": true, "tests/test_georeferencing.py::TestLS7InvalidZGroundMethod::test_run_georeferencing_invalid_z_ground_method_raises_error": true, "tests/test_georeferencing.py::TestLS7InvalidZGroundMethod::test_run_georeferencing_invalid_z_ground_method_error_message": true, "tests/test_main_pipeline.py::TestPipelineOrchestration::test_pipeline_config_error": true, "tests/test_main_pipeline.py::TestPipelineOrchestration::test_pipeline_unexpected_error": true, "tests/test_main_pipeline.py::TestConfigurationPassing::test_config_passed_to_submodules": true, "tests/test_main_pipeline.py::TestLoggingIntegration::test_logging_setup": true, "tests/test_main_pipeline.py::TestLS7ModuleLevelLogger::test_load_pipeline_config_uses_module_logger": true, "tests/test_main_pipeline.py::TestLS7ModuleLevelLogger::test_run_complete_pipeline_uses_module_logger_and_sets_up_logging": true, "tests/test_main_pipeline.py::TestLS7ModuleLevelLogger::test_module_logger_initialization_pattern": true, "tests/test_main_pipeline.py::TestLS7ModuleLevelLogger::test_logging_messages": true, "tests/test_main_pipeline.py::TestAdditionalErrorCoverage::test_consolidation_exception_coverage": true, "tests/test_main_pipeline.py::TestAdditionalErrorCoverage::test_georeferencing_exception_coverage": true, "tests/test_main_pipeline.py::TestAdditionalErrorCoverage::test_rgb_creation_exception_coverage": true, "tests/test_main_pipeline.py::TestAdditionalErrorCoverage::test_plotting_exception_coverage": true, "tests/test_main_pipeline.py::TestAdditionalErrorCoverage::test_unexpected_exception_coverage": true, "tests/test_main_pipeline.py::TestAdditionalErrorCoverage::test_consolidation_failure_coverage": true, "tests/test_main_pipeline.py::TestAdditionalErrorCoverage::test_georeferencing_failure_coverage": true, "tests/test_synchronize_hsi_webodm.py::TestRunSynchronization::test_run_synchronization_basic_functionality": true, "tests/test_synchronize_hsi_webodm.py::TestRunSynchronization::test_run_synchronization_with_time_threshold": true, "tests/test_synchronize_hsi_webodm.py::TestRunSynchronization::test_run_synchronization_missing_config_keys": true, "tests/test_synchronize_hsi_webodm.py::TestRunSynchronization::test_run_synchronization_file_not_found": true, "tests/test_vectorized_georef.py::TestCalculateSensorViewVectorsVectorized::test_calculate_sensor_view_vectors_valid_inputs": true, "tests/test_vectorized_georef.py::TestCalculateSensorViewVectorsVectorized::test_calculate_sensor_view_vectors_single_pixel": true, "tests/test_vectorized_georef.py::TestCalculateSensorViewVectorsVectorized::test_calculate_sensor_view_vectors_with_scale_and_offset": true, "tests/test_vectorized_georef.py::TestPerformanceBenchmark::test_log_vectorized_vs_iterative_performance": true, "tests/test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_prepare_rotation_matrices_vectorized_identity": true, "tests/test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_prepare_rotation_matrices_vectorized_90deg_x_rotation": true, "tests/test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_calculate_sensor_pixel_vectors_vectorized_boresight_center": true, "tests/test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_transform_vectors_to_world_vectorized_identity_rotations": true, "tests/test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_intersect_rays_with_horizontal_plane_vectorized_nadir_pointing": true, "tests/test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_intersect_rays_with_horizontal_plane_vectorized_ray_parallel_to_plane": true, "tests/test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_process_hsi_line_vectorized_flat_plane_nadir_simple": true}