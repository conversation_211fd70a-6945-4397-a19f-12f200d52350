"""
Unit tests for main pipeline functionality.

This module tests the centralized configuration loading and pipeline orchestration
as specified in LS2_3 and LS2_4 requirements.
"""

import pytest
import toml
import tempfile
import os
from pathlib import Path
from unittest.mock import patch, MagicMock, mock_open

# Import the modules to test
from src.hsi_pipeline.main_pipeline import load_pipeline_config, run_complete_pipeline
from src.hsi_pipeline.pipeline_exceptions import PipelineConfigError


class TestConfigurationLoading:
    """Test cases for configuration loading functionality."""
    
    def test_load_valid_config(self, tmp_path):
        """Test loading a valid configuration file."""
        # Arrange
        config_content = {
            "paths": {
                "hsi_data_directory": "data/HSI/",
                "output_directory": "output/"
            },
            "parameters": {
                "georeferencing": {
                    "boresight_roll_deg": 0.1
                }
            }
        }
        
        config_file = tmp_path / "test_config.toml"
        with open(config_file, "w") as f:
            toml.dump(config_content, f)
        
        # Act
        loaded_config = load_pipeline_config(str(config_file))
        
        # Assert
        assert loaded_config == config_content
        assert loaded_config["paths"]["hsi_data_directory"] == "data/HSI/"
        assert loaded_config["parameters"]["georeferencing"]["boresight_roll_deg"] == 0.1
    
    def test_load_nonexistent_config(self):
        """Test loading a non-existent configuration file."""
        # Arrange
        nonexistent_path = "nonexistent_config.toml"
        
        # Act & Assert
        with pytest.raises(PipelineConfigError, match="Configuration file not found"):
            load_pipeline_config(nonexistent_path)
    
    def test_load_invalid_toml_syntax(self, tmp_path):
        """Test loading a file with invalid TOML syntax."""
        # Arrange
        config_file = tmp_path / "invalid_config.toml"
        with open(config_file, "w") as f:
            f.write("invalid toml syntax [[[")
        
        # Act & Assert
        with pytest.raises(PipelineConfigError, match="Invalid TOML syntax"):
            load_pipeline_config(str(config_file))
    
    def test_load_config_with_unicode(self, tmp_path):
        """Test loading configuration with unicode characters."""
        # Arrange
        config_content = {
            "project_name": "HSI Georeferencing Pipeline",
            "paths": {
                "description": "Test with unicode: äöü"
            }
        }
        
        config_file = tmp_path / "unicode_config.toml"
        with open(config_file, "w", encoding="utf-8") as f:
            toml.dump(config_content, f)
        
        # Act
        loaded_config = load_pipeline_config(str(config_file))
        
        # Assert
        assert loaded_config["paths"]["description"] == "Test with unicode: äöü"


class TestPipelineOrchestration:
    """Test cases for pipeline orchestration functionality."""
    
    @patch('src.hsi_pipeline.main_pipeline.run_plotting')
    @patch('src.hsi_pipeline.main_pipeline.run_create_rgb_geotiff')
    @patch('src.hsi_pipeline.main_pipeline.run_georeferencing')
    @patch('src.hsi_pipeline.main_pipeline.run_synchronization')
    @patch('src.hsi_pipeline.main_pipeline.run_consolidation')
    @patch('src.hsi_pipeline.main_pipeline.load_pipeline_config')
    def test_successful_pipeline_execution(self, mock_load_config, mock_consolidation, 
                                         mock_synchronization, mock_georeferencing,
                                         mock_rgb_creation, mock_plotting):
        """Test successful execution of the complete pipeline."""
        # Arrange
        mock_config = {"test": "config"}
        mock_load_config.return_value = mock_config
        
        # All steps succeed
        mock_consolidation.return_value = True
        mock_synchronization.return_value = True
        mock_georeferencing.return_value = True
        mock_rgb_creation.return_value = True
        mock_plotting.return_value = True
        
        # Act
        result = run_complete_pipeline("test_config.toml")
        
        # Assert
        assert result is True
        mock_load_config.assert_called_once_with("test_config.toml")
        mock_consolidation.assert_called_once_with(mock_config)
        mock_synchronization.assert_called_once_with(mock_config)
        mock_georeferencing.assert_called_once_with(mock_config)
        mock_rgb_creation.assert_called_once_with(mock_config)
        mock_plotting.assert_called_once_with(mock_config)
    
    @patch('src.hsi_pipeline.main_pipeline.run_plotting')
    @patch('src.hsi_pipeline.main_pipeline.run_create_rgb_geotiff')
    @patch('src.hsi_pipeline.main_pipeline.run_georeferencing')
    @patch('src.hsi_pipeline.main_pipeline.run_synchronization')
    @patch('src.hsi_pipeline.main_pipeline.run_consolidation')
    @patch('src.hsi_pipeline.main_pipeline.load_pipeline_config')
    def test_pipeline_failure_in_critical_step(self, mock_load_config, mock_consolidation,
                                              mock_synchronization, mock_georeferencing,
                                              mock_rgb_creation, mock_plotting):
        """Test pipeline failure in a critical step."""
        # Arrange
        mock_config = {"test": "config"}
        mock_load_config.return_value = mock_config
        
        mock_consolidation.return_value = True
        mock_synchronization.return_value = False  # Critical step fails
        
        # Act
        result = run_complete_pipeline("test_config.toml")
        
        # Assert
        assert result is False
        mock_consolidation.assert_called_once_with(mock_config)
        mock_synchronization.assert_called_once_with(mock_config)
        # Subsequent steps should not be called
        mock_georeferencing.assert_not_called()
        mock_rgb_creation.assert_not_called()
        mock_plotting.assert_not_called()
    
    @patch('src.hsi_pipeline.main_pipeline.run_plotting')
    @patch('src.hsi_pipeline.main_pipeline.run_create_rgb_geotiff')
    @patch('src.hsi_pipeline.main_pipeline.run_georeferencing')
    @patch('src.hsi_pipeline.main_pipeline.run_synchronization')
    @patch('src.hsi_pipeline.main_pipeline.run_consolidation')
    @patch('src.hsi_pipeline.main_pipeline.load_pipeline_config')
    def test_pipeline_warning_in_optional_step(self, mock_load_config, mock_consolidation,
                                              mock_synchronization, mock_georeferencing,
                                              mock_rgb_creation, mock_plotting):
        """Test pipeline with warnings in optional steps."""
        # Arrange
        mock_config = {"test": "config"}
        mock_load_config.return_value = mock_config
        
        # Critical steps succeed
        mock_consolidation.return_value = True
        mock_synchronization.return_value = True
        mock_georeferencing.return_value = True
        
        # Optional steps fail
        mock_rgb_creation.return_value = False
        mock_plotting.return_value = False
        
        # Act
        result = run_complete_pipeline("test_config.toml")
        
        # Assert
        assert result is False  # Pipeline marked as not fully successful
        # All steps should be called
        mock_consolidation.assert_called_once()
        mock_synchronization.assert_called_once()
        mock_georeferencing.assert_called_once()
        mock_rgb_creation.assert_called_once()
        mock_plotting.assert_called_once()
    
    @patch('main_pipeline.load_pipeline_config')
    def test_pipeline_config_error(self, mock_load_config):
        """Test pipeline handling of configuration errors."""
        # Arrange
        mock_load_config.side_effect = PipelineConfigError("Config error")
        
        # Act
        result = run_complete_pipeline("invalid_config.toml")
        
        # Assert
        assert result is False
        mock_load_config.assert_called_once_with("invalid_config.toml")
    
    @patch('main_pipeline.run_consolidation')
    @patch('main_pipeline.load_pipeline_config')
    def test_pipeline_unexpected_error(self, mock_load_config, mock_consolidation):
        """Test pipeline handling of unexpected errors."""
        # Arrange
        mock_config = {"test": "config"}
        mock_load_config.return_value = mock_config
        mock_consolidation.side_effect = Exception("Unexpected error")
        
        # Act
        result = run_complete_pipeline("test_config.toml")
        
        # Assert
        assert result is False


class TestConfigurationPassing:
    """Test cases for configuration passing to sub-modules."""
    
    @patch('main_pipeline.run_georeferencing')
    @patch('main_pipeline.load_pipeline_config')
    def test_config_passed_to_submodules(self, mock_load_config, mock_georeferencing):
        """Test that configuration is passed correctly to sub-modules."""
        # Arrange
        test_config = {
            "paths": {"test_path": "value"},
            "parameters": {"test_param": 123}
        }
        mock_load_config.return_value = test_config
        mock_georeferencing.return_value = True
        
        # Mock other modules to avoid full pipeline execution
        with patch('main_pipeline.run_consolidation', return_value=True), \
             patch('main_pipeline.run_synchronization', return_value=True), \
             patch('main_pipeline.run_create_rgb_geotiff', return_value=True), \
             patch('main_pipeline.run_plotting', return_value=True):
            
            # Act
            run_complete_pipeline("test_config.toml")
        
        # Assert
        mock_georeferencing.assert_called_once_with(test_config)
    
    def test_config_object_immutability(self):
        """Test that config object is not modified by pipeline."""
        # This is more of a design principle test
        # In practice, we should ensure sub-modules don't modify the config
        original_config = {
            "paths": {"test": "value"},
            "parameters": {"test": 123}
        }
        
        # Create a copy to simulate what happens in the pipeline
        config_copy = original_config.copy()
        
        # Simulate sub-module receiving config
        def mock_submodule(config):
            # Sub-module should not modify the config
            return True
        
        # Act
        result = mock_submodule(config_copy)
        
        # Assert
        assert config_copy == original_config
        assert result is True


class TestLoggingIntegration:
    """Test cases for logging integration."""

    @patch('main_pipeline.setup_logging')
    @patch('main_pipeline.get_logger')
    def test_logging_setup(self, mock_get_logger, mock_setup_logging):
        """Test that logging is properly set up."""
        # Arrange
        mock_logger = MagicMock()
        mock_get_logger.return_value = mock_logger

        with patch('main_pipeline.load_pipeline_config', side_effect=PipelineConfigError("Test")):
            # Act
            run_complete_pipeline("test_config.toml")

        # Assert
        mock_setup_logging.assert_called_once_with(log_level="INFO", log_file="pipeline.log")
        mock_get_logger.assert_called()
        mock_logger.info.assert_called()
        mock_logger.error.assert_called()


# LS7_7: Tests for Module-Level Logger Usage

class TestLS7ModuleLevelLogger:
    """Test cases for LS7_7: Module-level logger initialization."""

    def test_load_pipeline_config_uses_module_logger(self, mocker, tmp_path):
        """Test that load_pipeline_config uses the module-level logger after refactoring."""
        # Arrange
        mock_module_logger = mocker.patch('main_pipeline.logger', spec=True)

        dummy_config_path = tmp_path / "dummy_config.toml"
        with open(dummy_config_path, "w") as f:
            f.write("[settings]\nkey = 'value'\n")  # Minimal valid TOML

        # Act
        load_pipeline_config(str(dummy_config_path))

        # Assert
        mock_module_logger.info.assert_called()
        # Check for specific log message if applicable
        mock_module_logger.info.assert_any_call(f"Successfully loaded configuration from: {dummy_config_path}")

    def test_run_complete_pipeline_uses_module_logger_and_sets_up_logging(self, mocker, tmp_path):
        """Test that run_complete_pipeline uses module-level logger and setup_logging is effective."""
        # Arrange
        mock_setup_logging = mocker.patch('main_pipeline.setup_logging')
        mock_module_logger_main = mocker.patch('main_pipeline.logger', spec=True)

        # Mock functions called within run_complete_pipeline to isolate logger testing
        mock_load_config = mocker.patch('main_pipeline.load_pipeline_config', return_value={'georeferencing_params': {}})
        mock_run_georef = mocker.patch('main_pipeline.run_georeferencing', return_value=True)
        mock_run_consolidation = mocker.patch('main_pipeline.run_consolidation', return_value=True)
        mock_run_synchronization = mocker.patch('main_pipeline.run_synchronization', return_value=True)
        mock_run_create_rgb = mocker.patch('main_pipeline.run_create_rgb_geotiff', return_value=True)
        mock_run_plotting = mocker.patch('main_pipeline.run_plotting', return_value=True)

        dummy_config_path = tmp_path / "dummy_config.toml"
        dummy_config_path.touch()

        # Act
        run_complete_pipeline(str(dummy_config_path))

        # Assert
        mock_setup_logging.assert_called_once()
        mock_module_logger_main.info.assert_any_call(f"Starting complete HSI Georeferencing Pipeline with configuration: {dummy_config_path}")
        mock_load_config.assert_called_once_with(str(dummy_config_path))
        mock_run_georef.assert_called_once()

    def test_module_logger_initialization_pattern(self, mocker):
        """Test that the module-level logger follows the expected pattern."""
        # This test verifies that the logger is initialized at module level
        # by checking that it's available without function calls

        # Arrange & Act
        # Import the module to trigger module-level initialization
        from src.hsi_pipeline import main_pipeline

        # Assert
        # The logger should be available as a module attribute
        assert hasattr(main_pipeline, 'logger')
        # The logger should be a Logger instance
        import logging
        assert isinstance(main_pipeline.logger, logging.Logger)
        # The logger name should match the module name
        assert main_pipeline.logger.name == 'main_pipeline'
    
    @patch('main_pipeline.get_logger')
    @patch('main_pipeline.setup_logging')
    def test_logging_messages(self, mock_setup_logging, mock_get_logger):
        """Test that appropriate log messages are generated."""
        # Arrange
        mock_logger = MagicMock()
        mock_get_logger.return_value = mock_logger
        
        with patch('main_pipeline.load_pipeline_config') as mock_load_config, \
             patch('main_pipeline.run_consolidation', return_value=True), \
             patch('main_pipeline.run_synchronization', return_value=True), \
             patch('main_pipeline.run_georeferencing', return_value=True), \
             patch('main_pipeline.run_create_rgb_geotiff', return_value=True), \
             patch('main_pipeline.run_plotting', return_value=True):
            
            mock_load_config.return_value = {"test": "config"}
            
            # Act
            run_complete_pipeline("test_config.toml")
        
        # Assert
        # Check that info messages were logged for each step
        info_calls = [call for call in mock_logger.info.call_args_list]
        assert len(info_calls) >= 5  # At least one for each pipeline step
        
        # Check for specific log messages
        log_messages = [str(call) for call in info_calls]
        assert any("Starting complete HSI Georeferencing Pipeline" in msg for msg in log_messages)


class TestAdditionalErrorCoverage:
    """Additional tests to improve coverage of error handling paths."""

    @patch('main_pipeline.run_consolidation')
    @patch('main_pipeline.run_synchronization')
    @patch('main_pipeline.run_georeferencing')
    @patch('main_pipeline.run_create_rgb_geotiff')
    @patch('main_pipeline.run_plotting')
    @patch('main_pipeline.load_pipeline_config')
    def test_consolidation_exception_coverage(self, mock_load_config, mock_plotting, mock_rgb,
                                            mock_georef, mock_sync, mock_consol):
        """Test exception in consolidation step (covers line 75)."""
        # Arrange
        mock_load_config.return_value = {'test': 'config'}
        mock_consol.side_effect = Exception("Consolidation failed")

        # Act
        result = run_complete_pipeline("test_config.toml")

        # Assert
        assert result is False
        mock_consol.assert_called_once()
        # Other steps should not be called due to early failure
        mock_sync.assert_not_called()
        mock_georef.assert_not_called()
        mock_rgb.assert_not_called()
        mock_plotting.assert_not_called()

    @patch('main_pipeline.run_consolidation')
    @patch('main_pipeline.run_synchronization')
    @patch('main_pipeline.run_georeferencing')
    @patch('main_pipeline.run_create_rgb_geotiff')
    @patch('main_pipeline.run_plotting')
    @patch('main_pipeline.load_pipeline_config')
    def test_georeferencing_exception_coverage(self, mock_load_config, mock_plotting, mock_rgb,
                                             mock_georef, mock_sync, mock_consol):
        """Test exception in georeferencing step (covers lines 97-99)."""
        # Arrange
        mock_load_config.return_value = {'test': 'config'}
        mock_consol.return_value = True
        mock_sync.return_value = True
        mock_georef.side_effect = Exception("Georeferencing failed")

        # Act
        result = run_complete_pipeline("test_config.toml")

        # Assert
        assert result is False
        mock_consol.assert_called_once()
        mock_sync.assert_called_once()
        mock_georef.assert_called_once()
        # Optional steps should not be called due to critical failure
        mock_rgb.assert_not_called()
        mock_plotting.assert_not_called()

    @patch('main_pipeline.run_consolidation')
    @patch('main_pipeline.run_synchronization')
    @patch('main_pipeline.run_georeferencing')
    @patch('main_pipeline.run_create_rgb_geotiff')
    @patch('main_pipeline.run_plotting')
    @patch('main_pipeline.load_pipeline_config')
    def test_rgb_creation_exception_coverage(self, mock_load_config, mock_plotting, mock_rgb,
                                           mock_georef, mock_sync, mock_consol):
        """Test exception in RGB creation step (covers lines 109-111)."""
        # Arrange
        mock_load_config.return_value = {'test': 'config'}
        mock_consol.return_value = True
        mock_sync.return_value = True
        mock_georef.return_value = True
        mock_rgb.side_effect = Exception("RGB creation failed")
        mock_plotting.return_value = True

        # Act
        result = run_complete_pipeline("test_config.toml")

        # Assert
        assert result is False  # Pipeline should continue but mark as not fully successful
        mock_consol.assert_called_once()
        mock_sync.assert_called_once()
        mock_georef.assert_called_once()
        mock_rgb.assert_called_once()
        mock_plotting.assert_called_once()  # Should continue to plotting

    @patch('main_pipeline.run_consolidation')
    @patch('main_pipeline.run_synchronization')
    @patch('main_pipeline.run_georeferencing')
    @patch('main_pipeline.run_create_rgb_geotiff')
    @patch('main_pipeline.run_plotting')
    @patch('main_pipeline.load_pipeline_config')
    def test_plotting_exception_coverage(self, mock_load_config, mock_plotting, mock_rgb,
                                       mock_georef, mock_sync, mock_consol):
        """Test exception in plotting step (covers lines 121-123)."""
        # Arrange
        mock_load_config.return_value = {'test': 'config'}
        mock_consol.return_value = True
        mock_sync.return_value = True
        mock_georef.return_value = True
        mock_rgb.return_value = True
        mock_plotting.side_effect = Exception("Plotting failed")

        # Act
        result = run_complete_pipeline("test_config.toml")

        # Assert
        assert result is False  # Pipeline should mark as not fully successful
        mock_consol.assert_called_once()
        mock_sync.assert_called_once()
        mock_georef.assert_called_once()
        mock_rgb.assert_called_once()
        mock_plotting.assert_called_once()

    @patch('main_pipeline.run_consolidation')
    @patch('main_pipeline.load_pipeline_config')
    def test_unexpected_exception_coverage(self, mock_load_config, mock_consol):
        """Test unexpected exception handling (covers lines 133-135)."""
        # Arrange
        mock_load_config.return_value = {'test': 'config'}
        mock_consol.side_effect = RuntimeError("Unexpected runtime error")

        # Act
        result = run_complete_pipeline("test_config.toml")

        # Assert
        assert result is False
        mock_consol.assert_called_once()

    @patch('main_pipeline.run_consolidation')
    @patch('main_pipeline.run_synchronization')
    @patch('main_pipeline.run_georeferencing')
    @patch('main_pipeline.run_create_rgb_geotiff')
    @patch('main_pipeline.run_plotting')
    @patch('main_pipeline.load_pipeline_config')
    def test_consolidation_failure_coverage(self, mock_load_config, mock_plotting, mock_rgb,
                                          mock_georef, mock_sync, mock_consol):
        """Test consolidation step returning False (covers line 75)."""
        # Arrange
        mock_load_config.return_value = {'test': 'config'}
        mock_consol.return_value = False  # Consolidation returns False

        # Act
        result = run_complete_pipeline("test_config.toml")

        # Assert
        assert result is False
        mock_consol.assert_called_once()
        # Other steps should not be called due to early failure
        mock_sync.assert_not_called()
        mock_georef.assert_not_called()
        mock_rgb.assert_not_called()
        mock_plotting.assert_not_called()

    @patch('main_pipeline.run_consolidation')
    @patch('main_pipeline.run_synchronization')
    @patch('main_pipeline.run_georeferencing')
    @patch('main_pipeline.run_create_rgb_geotiff')
    @patch('main_pipeline.run_plotting')
    @patch('main_pipeline.load_pipeline_config')
    def test_georeferencing_failure_coverage(self, mock_load_config, mock_plotting, mock_rgb,
                                           mock_georef, mock_sync, mock_consol):
        """Test georeferencing step returning False (covers line 95)."""
        # Arrange
        mock_load_config.return_value = {'test': 'config'}
        mock_consol.return_value = True
        mock_sync.return_value = True
        mock_georef.return_value = False  # Georeferencing returns False

        # Act
        result = run_complete_pipeline("test_config.toml")

        # Assert
        assert result is False
        mock_consol.assert_called_once()
        mock_sync.assert_called_once()
        mock_georef.assert_called_once()
        # Optional steps should not be called due to critical failure
        mock_rgb.assert_not_called()
        mock_plotting.assert_not_called()


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])
